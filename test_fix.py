#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml.html import fromstring
from service.html_to_vform import HtmlToVFormConverter

def test_table_duplication():
    """测试表格重复问题修复"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print("开始测试表格重复问题修复...")
    
    # 创建转换器并转换
    converter = HtmlToVFormConverter()
    result = converter.convert(html_content)
    
    # 统计表格数量
    table_count = 0
    def count_tables(obj):
        nonlocal table_count
        if isinstance(obj, dict):
            if obj.get('type') == 'table':
                table_count += 1
            for value in obj.values():
                count_tables(value)
        elif isinstance(obj, list):
            for item in obj:
                count_tables(item)
    
    count_tables(result)
    
    print(f"修复后生成的表格数量: {table_count}")
    print(f"原始HTML中的表格数量: 3")
    
    if table_count == 3:
        print("✅ 表格重复问题已修复！")
        return True
    else:
        print("❌ 表格重复问题仍然存在")
        return False

if __name__ == "__main__":
    test_table_duplication()