#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

from lxml.html import fromstring
import json

def simple_html_analysis():
    """简单分析HTML结构"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取body元素
    body = html_tree.find('.//body')
    
    # 查找所有表格
    tables = body.xpath('.//table')
    print(f"找到 {len(tables)} 个表格")
    
    # 分析每个表格
    for i, table in enumerate(tables):
        print(f"\n=== 表格 {i+1} ===")
        
        # 检查是否为嵌套表格
        ancestor_tables = table.xpath('./ancestor::table')
        is_nested = len(ancestor_tables) > 0
        print(f"表格类型: {'嵌套表格' if is_nested else '顶层表格'}")
        
        # 获取表格的行
        rows = table.xpath('.//tr')
        print(f"行数: {len(rows)}")
        
        # 查找包含绕组的单元格
        winding_cells = table.xpath('.//td[contains(.//text(), "绕组")]')
        print(f"包含绕组的单元格数量: {len(winding_cells)}")
        
        # 显示包含绕组的单元格内容
        for j, cell in enumerate(winding_cells):
            cell_text = cell.text_content().strip()
            print(f"  绕组单元格 {j+1}: {cell_text[:50]}...")
        
        # 查找嵌套表格
        nested_tables_in_table = table.xpath('.//table')
        if nested_tables_in_table:
            print(f"包含嵌套表格数量: {len(nested_tables_in_table)}")
            for k, nested_table in enumerate(nested_tables_in_table):
                nested_rows = nested_table.xpath('.//tr')
                print(f"  嵌套表格 {k+1} 行数: {len(nested_rows)}")
                
                # 查找嵌套表格中的绕组
                nested_winding_cells = nested_table.xpath('.//td[contains(.//text(), "绕组")]')
                print(f"  嵌套表格 {k+1} 包含绕组的单元格数量: {len(nested_winding_cells)}")

if __name__ == "__main__":
    simple_html_analysis()