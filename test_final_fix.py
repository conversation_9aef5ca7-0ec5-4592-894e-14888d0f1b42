#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.html_to_vform import HtmlToVFormConverter
import json

def test_fixed_conversion():
    """测试修复后的转换功能"""
    print("=== 测试修复后的HTML转VForm转换 ===")
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"HTML文件读取成功，长度: {len(html_content)} 字符")
    
    # 创建转换器
    converter = HtmlToVFormConverter()
    
    # 转换HTML
    result = converter.convert(html_content)
    
    # 统计表格数量
    table_count = 0
    def count_tables(obj):
        nonlocal table_count
        if isinstance(obj, dict):
            if obj.get('type') == 'table':
                table_count += 1
            for value in obj.values():
                count_tables(value)
        elif isinstance(obj, list):
            for item in obj:
                count_tables(item)
    
    count_tables(result)
    
    print(f"\n=== 结果 ===")
    print(f"生成的表格数量: {table_count}")
    print(f"HTML中的表格数量: 3")
    
    if table_count == 3:
        print("✅ 表格重复问题已修复！")
        
        # 保存结果
        with open('service/json/vform_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("修复后的Vform文件已保存到: service/json/vform_fixed.json")
        
        return True
    else:
        print(f"❌ 表格重复问题仍然存在，生成了 {table_count} 个表格")
        return False

if __name__ == "__main__":
    test_fixed_conversion()