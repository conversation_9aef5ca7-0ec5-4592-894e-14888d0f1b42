#!/usr/bin/env python3
"""
测试表格重复生成问题的修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml.html import fromstring
from service.html_to_vform import HtmlToVFormConverter
import json

def test_nested_table_duplication():
    """测试嵌套表格重复处理的问题"""
    
    # 创建包含嵌套表格的HTML
    html_content = """
    <html>
    <body>
        <table>
            <tr>
                <td>外层表格单元格1</td>
                <td>
                    <table>
                        <tr>
                            <td>嵌套表格单元格1</td>
                            <td>嵌套表格单元格2</td>
                        </tr>
                        <tr>
                            <td>嵌套表格单元格3</td>
                            <td>嵌套表格单元格4</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>外层表格单元格2</td>
                <td>外层表格单元格3</td>
            </tr>
        </table>
    </body>
    </html>
    """
    
    print("=== 测试嵌套表格重复处理问题 ===")
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 统计HTML中的表格数量
    all_tables = html_tree.xpath('//table')
    top_level_tables = html_tree.xpath('//table[not(ancestor::table)]')
    nested_tables = html_tree.xpath('//table[ancestor::table]')
    
    print(f"HTML中总表格数量: {len(all_tables)}")
    print(f"顶层表格数量: {len(top_level_tables)}")
    print(f"嵌套表格数量: {len(nested_tables)}")
    
    # 转换为vForm
    converter = HtmlToVFormConverter()
    result = converter.convert(html_content)
    
    # 统计生成的表格组件数量
    def count_table_components(obj, path=""):
        """递归统计表格组件数量"""
        table_count = 0
        
        if isinstance(obj, dict):
            if obj.get('type') == 'table':
                table_count += 1
                print(f"找到表格组件: {path}, key={obj.get('key')}")
                
                # 统计表格中的行和单元格
                rows = obj.get('rows', [])
                print(f"  表格行数: {len(rows)}")
                
                total_cells = 0
                for i, row in enumerate(rows):
                    cells = row.get('cols', [])
                    total_cells += len(cells)
                    print(f"  第{i+1}行单元格数: {len(cells)}")
                
                print(f"  总单元格数: {total_cells}")
                
            for k, v in obj.items():
                table_count += count_table_components(v, f"{path}.{k}" if path else k)
                
        elif isinstance(obj, list):
            for i, v in enumerate(obj):
                table_count += count_table_components(v, f"{path}[{i}]")
                
        return table_count
    
    table_component_count = count_table_components(result)
    print(f"\n生成的表格组件总数: {table_component_count}")
    
    # 检查是否有重复的表格内容
    def find_duplicate_content(obj, content_list=None):
        """查找重复的表格内容"""
        if content_list is None:
            content_list = []
            
        if isinstance(obj, dict):
            if obj.get('type') == 'static-text':
                text_content = obj.get('options', {}).get('textContent', '')
                if text_content.strip():
                    content_list.append(text_content.strip())
                    
            for v in obj.values():
                find_duplicate_content(v, content_list)
                
        elif isinstance(obj, list):
            for v in obj:
                find_duplicate_content(v, content_list)
                
        return content_list
    
    all_text_content = find_duplicate_content(result)
    
    # 检查是否有重复的文本内容
    content_counts = {}
    for content in all_text_content:
        content_counts[content] = content_counts.get(content, 0) + 1
    
    duplicates = {k: v for k, v in content_counts.items() if v > 1}
    
    if duplicates:
        print(f"\n发现重复内容:")
        for content, count in duplicates.items():
            print(f"  '{content}' 出现 {count} 次")
    else:
        print(f"\n✓ 没有发现重复内容")
    
    # 验证结果
    expected_table_count = len(top_level_tables)  # 应该只有顶层表格被处理
    
    print(f"\n=== 验证结果 ===")
    print(f"期望的表格组件数量: {expected_table_count}")
    print(f"实际的表格组件数量: {table_component_count}")
    
    if table_component_count == expected_table_count:
        print("✓ 表格数量正确，没有重复处理")
    else:
        print("✗ 表格数量不正确，可能存在重复处理")
    
    if not duplicates:
        print("✓ 没有重复的内容")
    else:
        print("✗ 存在重复的内容")
    
    return table_component_count == expected_table_count and not duplicates

if __name__ == "__main__":
    success = test_nested_table_duplication()
    if success:
        print("\n🎉 测试通过！表格重复处理问题已修复。")
    else:
        print("\n❌ 测试失败！表格重复处理问题仍然存在。")
