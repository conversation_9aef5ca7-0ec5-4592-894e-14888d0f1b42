#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_problematic_cell():
    """分析有问题的单元格"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取主表格的第2行第1个单元格
    main_table = html_tree.xpath('//table')[0]
    rows = main_table.xpath('.//tr')
    
    if len(rows) >= 2:
        problematic_cell = rows[1].xpath('./td')[0]
        
        print("=== 问题单元格分析 ===")
        print(f"单元格直接文本内容: '{problematic_cell.text or ''}'")
        
        # 分析单元格的子元素
        children = list(problematic_cell)
        print(f"单元格子元素数量: {len(children)}")
        
        for i, child in enumerate(children):
            print(f"\n子元素 {i+1}:")
            print(f"  标签: {child.tag}")
            print(f"  文本内容: '{child.text or ''}'")
            
            if child.tag == 'table':
                print(f"  这是一个嵌套表格")
                nested_rows = child.xpath('.//tr')
                print(f"  嵌套表格行数: {len(nested_rows)}")
            else:
                print(f"  内容: '{child.text_content()[:100]}...'")
        
        # 检查单元格的完整文本内容
        full_text = problematic_cell.text_content()
        print(f"\n单元格完整文本内容长度: {len(full_text)}")
        print(f"前200个字符: {full_text[:200]}")
        
        # 检查是否包含"检测要求"和"测量或观察结果"
        if '检测要求' in full_text:
            print(f"  包含'检测要求'")
        if '测量或观察结果' in full_text:
            print(f"  包含'测量或观察结果'")

if __name__ == "__main__":
    analyze_problematic_cell()