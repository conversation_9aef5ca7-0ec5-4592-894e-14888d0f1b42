#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_html_structure():
    """分析HTML结构，找出所有表格"""
    print("=== 分析HTML结构 ===")
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    html_tree = fromstring(html_content)
    
    # 查找所有表格
    all_tables = html_tree.xpath('//table')
    print(f"找到 {len(all_tables)} 个表格")
    
    for i, table in enumerate(all_tables):
        print(f"\n表格 {i+1}:")
        print(f"  ID: {id(table)}")
        print(f"  行数: {len(table.xpath('.//tr'))}")
        
        # 查找父元素
        parent = table.getparent()
        if parent is not None:
            print(f"  父元素: {parent.tag}")
        
        # 查找表格周围的div结构
        div_parents = []
        current = table
        while current is not None:
            parent = current.getparent()
            if parent is not None and parent.tag == 'div':
                div_parents.append(parent)
            current = parent
        
        print(f"  上层div数量: {len(div_parents)}")
    
    # 分析DOM结构
    body = html_tree.find('.//body')
    if body is not None:
        print(f"\n=== 分析body结构 ===")
        analyze_element(body, 0)

def analyze_element(element, depth):
    """递归分析元素结构"""
    indent = "  " * depth
    element_tag = element.tag if hasattr(element, 'tag') else 'unknown'
    
    if element_tag == 'table':
        print(f"{indent}表格 (ID: {id(element)})")
    else:
        print(f"{indent}{element_tag}")
    
    # 分析子元素
    child_nodes = element.xpath('node()')
    for node in child_nodes:
        if hasattr(node, 'tag'):  # 元素节点
            analyze_element(node, depth + 1)

if __name__ == "__main__":
    analyze_html_structure()