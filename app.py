from sqlite3.dbapi2 import Timestamp
from flask import Flask, render_template, request, jsonify, stream_with_context, Response, send_file
import json
from service.read_word import convert_placeholders_stream
from service.aspose_word_to_html import AsposeWordToHtmlConverter
from service.html_to_vform import HtmlToVFormConverter
from io import BytesIO
import logging
from datetime import datetime
import os
from dotenv import load_dotenv

# 设置Flask应用
app = Flask(__name__)
# 设置日志级别为DEBUG，确保所有日志可见
app.logger.setLevel(logging.DEBUG)
# 配置基本日志格式
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 加载环境变量和配置
def configure_app(app):
    # 获取当前环境
    env = os.environ.get('FLASK_ENV', 'development')
    app.logger.info(f"当前运行环境: {env}")
    
    # 根据环境加载对应的.env文件
    env_file = f'.env.{env}'
    fallback_env_file = '.env'
    
    # 尝试加载特定环境的.env文件，如果不存在则加载默认.env文件
    if os.path.exists(env_file):
        load_dotenv(env_file)
        app.logger.info(f"已加载环境配置: {env_file}")
    elif os.path.exists(fallback_env_file):
        load_dotenv(fallback_env_file)
        app.logger.info(f"已加载默认环境配置: {fallback_env_file}")
    else:
        app.logger.warning("未找到.env文件，将使用默认配置")
    
    # 从环境变量中加载配置到Flask app.config
    app.config['UPLOAD_API_URL'] = os.environ.get('UPLOAD_API_URL')
    app.config['WORD_TO_HTML_API_URL'] = os.environ.get('WORD_TO_HTML_API_URL')
    
    # 验证必要的配置是否存在
    if app.config['UPLOAD_API_URL'] is None or app.config['WORD_TO_HTML_API_URL'] is None:
        app.logger.error("缺少必要的API配置。请确保在.env文件中设置了UPLOAD_API_URL和WORD_TO_HTML_API_URL。")
    
    # 输出当前配置信息
    app.logger.info(f"上传API地址: {app.config['UPLOAD_API_URL']}")
    app.logger.info(f"Word转HTML API地址: {app.config['WORD_TO_HTML_API_URL']}")

# 初始化配置
configure_app(app)


@app.route('/')
def greet():
    return render_template('index.html')

# 下载生成的占位符文件
@app.route('/download', methods=['POST'])
def download():
    try:
        if 'file' not in request.files:
            app.logger.error('No file found in request')
            return jsonify({
                'status': 400,
                'message': '没有找到上传的文件'
            })
            
        file = request.files['file']
        app.logger.debug(f'Received file: {file.filename}')
        
        if not file.filename.endswith('.docx'):
            app.logger.error(f'Invalid file type: {file.filename}')
            return jsonify({
                'status': 400,
                'message': '只支持.docx格式的文件'
            })
        
        # 生成占位符
        app.logger.debug('Converting placeholders...')
        output_stream = convert_placeholders_stream(file)
        app.logger.debug('Placeholders converted successfully')
        
        # 添加时间戳到文件名
        timestamp = datetime.now().strftime('%Y%m%H%M%S')
        original_name = file.filename.replace('.docx', '') + '_' + timestamp + '.docx'
        
        app.logger.debug(f'Generated filename: {original_name}')
        
        # 下载占位符文件
        app.logger.debug('Sending file for download...')
        response = send_file(
            output_stream,
            as_attachment=True,
            download_name=original_name,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
        # 设置Content-Disposition头，使用URL编码处理中文文件名
        from urllib.parse import quote
        encoded_filename = quote(original_name)
        response.headers['Content-Disposition'] = f'attachment; filename={encoded_filename}'
        return response
    except Exception as e:
        app.logger.error(f'Error in download endpoint: {str(e)}', exc_info=True)
        return jsonify({
            'status': 500,
            'message': f'处理失败: {str(e)}'
        })
        
# 修改转换流程：Word -> HTML -> vForm
@app.route('/convert_via_html', methods=['POST'])
def convert_via_html():
    if 'file' not in request.files:
        return jsonify({
            'status': 400,
            'message': '没有找到上传的文件'
        })
        
    file = request.files['file']
    if not file.filename.endswith('.docx'):
        return jsonify({
            'status': 400,
            'message': '只支持.docx格式的文件'
        })

    def generate():
        try:
            # 创建转换器实例，使用Aspose库
            file_content = file.read()
            byte_io = BytesIO(file_content)
            
            # 先生成占位符
            placeholder_io = convert_placeholders_stream(byte_io)
            
            # 从Word转换到HTML
            app.logger.debug('Converting Word to HTML with placeholders using Aspose...')
            html_content = AsposeWordToHtmlConverter().convert_to_html(placeholder_io)
            
            # 保存中间HTML用于调试
            output_dir = 'service/json'
            os.makedirs(output_dir, exist_ok=True)
            if html_content:
                with open(os.path.join(output_dir, 'converted_to_html.html'), 'w', encoding='utf-8') as f:
                    f.write(html_content)
            else:
                app.logger.error('HTML转换失败: convert_to_html返回了None')
                yield json.dumps({
                    'status': 500,
                    'message': 'HTML转换失败: 无法从Word文档生成HTML'
                }, ensure_ascii=False)
                return
            
            # 否则继续转换为vForm
            # 使用HTML到vForm转换器，保持完整表格结构
            app.logger.debug('Converting HTML to vForm JSON...')
            vform_json = HtmlToVFormConverter().convert(html_content)
            app.logger.debug('HTML到vForm转换完成')
            
            # 保存转换后的vForm JSON用于调试
            with open(os.path.join(output_dir, 'vform_from_html.json'), 'w', encoding='utf-8') as f:
                json.dump(vform_json, f, ensure_ascii=False, indent=4)
            
            yield json.dumps({
                'status': 200,
                'message': '文件处理成功 (HTML->vForm)',
                'data': vform_json
            }, ensure_ascii=False)
            
        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            app.logger.error(f"处理失败: {str(e)}\n{error_trace}")
            yield json.dumps({
                'status': 500,
                'message': f'处理失败: {str(e)}'
            }, ensure_ascii=False)

    return Response(
        stream_with_context(generate()),
        content_type='application/json; charset=utf-8'
    )

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)