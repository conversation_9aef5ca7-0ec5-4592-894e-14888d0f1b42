#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def analyze_winding_occurrences():
    """分析绕组出现的具体情况"""
    
    # 读取JSON文件
    with open('service/json/vform_from_html.json', 'r', encoding='utf-8') as f:
        vform_data = json.load(f)
    
    def find_winding_contexts(obj, path="", results=None):
        if results is None:
            results = []
        
        if isinstance(obj, dict):
            if obj.get('type') == 'static-text':
                text_content = obj.get('options', {}).get('textContent', '')
                if '绕组' in text_content:
                    # 获取更多上下文信息
                    results.append({
                        'path': path,
                        'full_text': text_content,
                        'context_type': 'static-text'
                    })
            
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                find_winding_contexts(value, new_path, results)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_path = f"{path}[{i}]" if path else f"[{i}]"
                find_winding_contexts(item, new_path, results)
        
        return results
    
    winding_contexts = find_winding_contexts(vform_data)
    
    print("=== 绕组出现情况分析 ===")
    for i, context in enumerate(winding_contexts):
        print(f"\n绕组 {i+1}:")
        print(f"  路径: {context['path']}")
        print(f"  完整文本: {context['full_text']}")
        
        # 分析路径来判断是否合理
        if '嵌套表格' in context['path'] or 'nested' in context['path']:
            print(f"  类型: 嵌套表格中的绕组")
        elif 'rows[6]' in context['path']:
            print(f"  类型: 主表格表头中的绕组")
        elif 'rows[5]' in context['path']:
            print(f"  类型: 主表格内容中的绕组描述")
        else:
            print(f"  类型: 其他")
    
    # 判断是否合理
    nested_count = sum(1 for c in winding_contexts if '嵌套表格' in c['path'] or 'widgetList[5]' in c['path'])
    main_table_count = sum(1 for c in winding_contexts if 'rows[6]' in c['path'])
    description_count = sum(1 for c in winding_contexts if 'rows[5]' in c['path'])
    
    print(f"\n=== 分析结果 ===")
    print(f"嵌套表格中的绕组: {nested_count} 处")
    print(f"主表格表头中的绕组: {main_table_count} 处")
    print(f"主表格内容中的绕组描述: {description_count} 处")
    
    if nested_count == 1 and main_table_count == 1 and description_count == 1:
        print("✅ 绕组出现次数合理：嵌套表格1处 + 主表格表头1处 + 内容描述1处")
    else:
        print("⚠️ 绕组出现次数可能不合理")

if __name__ == "__main__":
    analyze_winding_occurrences()