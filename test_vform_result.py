#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def test_vform_conversion():
    """测试vForm转换结果"""
    
    # 检查是否存在转换后的JSON文件
    json_file = 'service/json/vform_from_html.json'
    if not os.path.exists(json_file):
        print("JSON文件不存在，需要先生成")
        return
    
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        vform_data = json.load(f)
    
    # 分析widgetList中的表格
    widgets = vform_data.get('widgetList', [])
    
    tables = []
    static_texts_with_winding = []
    
    def analyze_widget(widget, path=""):
        """分析组件"""
        if isinstance(widget, dict):
            widget_type = widget.get('type', '')
            
            if widget_type == 'table':
                tables.append({
                    'path': path,
                    'key': widget.get('key'),
                    'rows_count': len(widget.get('rows', []))
                })
                
                # 分析表格中的行
                for i, row in enumerate(widget.get('rows', [])):
                    for j, cell in enumerate(row.get('cols', [])):
                        cell_path = f"{path}.rows[{i}].cols[{j}]"
                        analyze_widget(cell, cell_path)
                        
            elif widget_type == 'static-text':
                text_content = widget.get('options', {}).get('textContent', '')
                if '绕组' in text_content:
                    static_texts_with_winding.append({
                        'path': path,
                        'text': text_content[:50] + '...' if len(text_content) > 50 else text_content
                    })
            
            # 递归分析子组件
            for key, value in widget.items():
                if key not in ['options', 'rows', 'cols']:
                    analyze_widget(value, f"{path}.{key}" if path else key)
        
        elif isinstance(widget, list):
            for i, item in enumerate(widget):
                analyze_widget(item, f"{path}[{i}]")
    
    # 分析所有组件
    for i, widget in enumerate(widgets):
        analyze_widget(widget, f"widgetList[{i}]")
    
    # 输出结果
    print("=== vForm转换结果分析 ===")
    print(f"总组件数量: {len(widgets)}")
    print(f"表格数量: {len(tables)}")
    print(f"包含'绕组'的静态文本数量: {len(static_texts_with_winding)}")
    
    print(f"\n=== 表格详情 ===")
    for i, table in enumerate(tables):
        print(f"表格 {i+1}:")
        print(f"  路径: {table['path']}")
        print(f"  Key: {table['key']}")
        print(f"  行数: {table['rows_count']}")
    
    print(f"\n=== 包含'绕组'的文本 ===")
    for i, text_info in enumerate(static_texts_with_winding):
        print(f"文本 {i+1}:")
        print(f"  路径: {text_info['path']}")
        print(f"  内容: {text_info['text']}")
    
    # 检查是否有重复的"绕组"文本
    if len(static_texts_with_winding) > 1:
        print(f"\n⚠️  警告：发现多个包含'绕组'的文本，可能存在重复问题")
    elif len(static_texts_with_winding) == 1:
        print(f"\n✅ 成功：只发现一个包含'绕组'的文本")
    else:
        print(f"\n❌ 错误：没有找到包含'绕组'的文本")

if __name__ == "__main__":
    test_vform_conversion()