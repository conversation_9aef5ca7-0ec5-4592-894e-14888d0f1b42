#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

from lxml.html import fromstring
import json
from service.enhanced_table_parser import EnhancedTableParser

def test_table_parsing():
    """测试表格解析功能"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取body元素
    body = html_tree.find('.//body')
    
    # 创建表格解析器
    table_parser = EnhancedTableParser()
    
    # 查找所有表格
    tables = body.xpath('.//table')
    print(f"找到 {len(tables)} 个表格")
    
    # 解析每个表格
    for i, table in enumerate(tables):
        print(f"\n=== 表格 {i+1} ===")
        
        # 检查是否为嵌套表格
        ancestor_tables = table.xpath('./ancestor::table')
        is_nested = len(ancestor_tables) > 0
        print(f"表格类型: {'嵌套表格' if is_nested else '顶层表格'}")
        
        # 解析表格
        vform_table = table_parser.parse_table(table)
        
        if vform_table:
            # 统计行数
            rows = vform_table.get('rows', [])
            print(f"生成行数: {len(rows)}")
            
            # 检查是否有重复的绕组内容
            winding_count = 0
            for row_idx, row in enumerate(rows):
                cols = row.get('cols', [])
                for col_idx, col in enumerate(cols):
                    widget_list = col.get('widgetList', [])
                    for widget_idx, widget in enumerate(widget_list):
                        if widget.get('type') == 'static-text':
                            text_content = widget.get('options', {}).get('textContent', '')
                            if '绕组' in text_content:
                                winding_count += 1
                                print(f"  绕组内容出现在: 行{row_idx}, 列{col_idx}, 位置{widget_idx}")
                                print(f"    内容: {text_content[:50]}...")
            
            print(f"绕组出现次数: {winding_count}")
        else:
            print("表格解析失败")

if __name__ == "__main__":
    test_table_parsing()