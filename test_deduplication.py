#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml.html import fromstring
import json

# 模拟转换器，只测试表格处理
class TestConverter:
    def __init__(self):
        self.counter = 1
        self.processed_tables = set()
    
    def generate_id(self):
        self.counter += 1
        return self.counter
    
    def convert_html_to_vform(self, html_content):
        """转换HTML到VForm"""
        html_tree = fromstring(html_content)
        
        form_config = {
            "modelName": "formData",
            "refName": "vForm",
            "rulesName": "rules",
            "labelWidth": 80,
            "labelPosition": "top",
            "size": "",
            "labelAlign": "label-left-align",
            "cssCode": "",
            "customClass": [],
            "functions": "",
            "layoutType": "PC",
            "jsonVersion": 3,
            "dataSources": [],
            "onFormCreated": "",
            "onFormMounted": "",
            "onFormDataChange": "",
            "onFormValidate": ""
        }
        
        widget_list = []
        
        # 处理body内容
        body = html_tree.find('.//body')
        if body is not None:
            self._process_body_content(body, widget_list)
        
        form_config["currentId"] = self.counter
        
        return {
            "widgetList": widget_list,
            "formConfig": form_config
        }
    
    def _process_body_content(self, element, items_list):
        """处理HTML元素及其所有子元素，按顺序转换为vForm组件"""
        element_tag = element.tag if hasattr(element, 'tag') else 'unknown'
        print(f"处理元素: {element_tag}")
        
        # 首先处理当前元素的直接子表格
        direct_tables = element.xpath('./table')
        if direct_tables:
            print(f"  在 {element_tag} 中找到 {len(direct_tables)} 个直接子表格")
            for table in direct_tables:
                vform_item = self._process_table(table, items_list)
                if vform_item:
                    items_list.append(vform_item)
        
        # 然后递归处理所有子元素
        child_nodes = element.xpath('node()')
        
        for node in child_nodes:
            if hasattr(node, 'tag'):  # 元素节点
                node_tag = node.tag
                if node_tag in ['div', 'td', 'body', 'p', 'tr']:
                    # 处理容器，递归处理其内容
                    self._process_body_content(node, items_list)
    
    def _process_table(self, html_table, items_list=None):
        """处理表格"""
        table_id = id(html_table)
        if table_id in self.processed_tables:
            print(f"表格 {table_id} 已处理过，跳过")
            return None
        
        self.processed_tables.add(table_id)
        print(f"处理表格 {table_id}")
        
        # 简化的表格结构
        table_node = {
            "key": self.generate_id(),
            "type": "table",
            "category": "container",
            "icon": "table",
            "rows": [],
            "options": {
                "name": f"table-{self.generate_id()}",
                "hidden": False,
                "customClass": []
            },
            "id": f"table-{self.generate_id()}"
        }
        
        # 获取所有行
        rows = html_table.xpath('.//tr')
        print(f"  表格有 {len(rows)} 行")
        
        # 处理表格单元格中的嵌套表格
        nested_tables = html_table.xpath('.//table')
        if nested_tables:
            print(f"  表格中有 {len(nested_tables)} 个嵌套表格")
            for nested_table in nested_tables:
                nested_item = self._process_table(nested_table, items_list)
                if nested_item and items_list is not None:
                    # 将嵌套表格添加到结果列表中
                    items_list.append(nested_item)
                    print(f"    添加嵌套表格到结果列表")
        
        return table_node

def main():
    print("=== 测试修复后的表格处理 ===")
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"HTML文件读取成功，长度: {len(html_content)} 字符")
    
    # 创建转换器
    converter = TestConverter()
    
    # 转换HTML
    result = converter.convert_html_to_vform(html_content)
    
    # 统计表格数量
    table_count = 0
    def count_tables(obj):
        nonlocal table_count
        if isinstance(obj, dict):
            if obj.get('type') == 'table':
                table_count += 1
            for value in obj.values():
                count_tables(value)
        elif isinstance(obj, list):
            for item in obj:
                count_tables(item)
    
    count_tables(result)
    
    print(f"\n=== 结果 ===")
    print(f"生成的表格数量: {table_count}")
    print(f"HTML中的表格数量: 3")
    
    if table_count == 3:
        print("✅ 表格重复问题已修复！")
        
        # 保存结果
        with open('service/json/vform_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("修复后的Vform文件已保存到: service/json/vform_fixed.json")
        
        return True
    else:
        print(f"❌ 表格重复问题仍然存在，生成了 {table_count} 个表格")
        return False

if __name__ == "__main__":
    main()