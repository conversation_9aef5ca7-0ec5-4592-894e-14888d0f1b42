#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_html_structure():
    """分析HTML结构"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 查找所有表格
    tables = html_tree.xpath('//table')
    
    print(f"HTML中找到 {len(tables)} 个表格")
    
    # 分析每个表格的结构
    for i, table in enumerate(tables):
        print(f"\n=== 表格 {i+1} ===")
        
        # 获取表格的行数
        rows = table.xpath('.//tr')
        print(f"行数: {len(rows)}")
        
        # 查找表格中的嵌套表格
        nested_tables = table.xpath('.//table')
        if nested_tables:
            print(f"包含 {len(nested_tables)} 个嵌套表格")
            for j, nested in enumerate(nested_tables):
                nested_rows = nested.xpath('.//tr')
                print(f"  嵌套表格 {j+1}: {len(nested_rows)} 行")
        
        # 查找包含"绕组"文本的单元格
        winding_cells = table.xpath('.//td[contains(text(), "绕组")]')
        if winding_cells:
            print(f"找到 {len(winding_cells)} 个包含'绕组'的单元格")
            for cell in winding_cells:
                print(f"  - 单元格内容: {cell.text_content().strip()}")
                
                # 查找这个单元格的父表格
                parent_table = cell.xpath('./ancestor::table')
                print(f"    父表格层级: {len(parent_table)}")
    
    # 分析表格的层级关系
    print(f"\n=== 表格层级关系 ===")
    
    def get_table_path(table):
        """获取表格的路径"""
        path = []
        current = table
        while current is not None:
            parent_tables = current.xpath('./ancestor::table')
            if parent_tables:
                path.append(f"table[{len(parent_tables)}]")
                current = parent_tables[0]
            else:
                break
        return " > ".join(reversed(path))
    
    for i, table in enumerate(tables):
        path = get_table_path(table)
        print(f"表格 {i+1}: {path}")

if __name__ == "__main__":
    analyze_html_structure()