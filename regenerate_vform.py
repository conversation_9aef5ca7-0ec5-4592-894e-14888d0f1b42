#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

# 暂时重命名有问题的文件，以便导入
os.rename('service/read_word.py', 'service/read_word.py.tmp')
os.rename('service/base64_to_file.py', 'service/base64_to_file.py.tmp')

try:
    from service.html_to_vform import HtmlToVFormConverter
    import json
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 创建转换器
    converter = HtmlToVFormConverter()
    
    # 转换HTML到vForm
    vform_data = converter.convert(html_content)
    
    # 保存结果
    with open('service/json/vform_from_html.json', 'w', encoding='utf-8') as f:
        json.dump(vform_data, f, ensure_ascii=False, indent=2)
    
    print("vForm JSON重新生成完成！")
    
    # 统计表格数量
    widget_list = vform_data.get('widgetList', [])
    table_count = sum(1 for widget in widget_list if widget.get('type') == 'table')
    print(f"生成的表格数量: {table_count}")

finally:
    # 恢复文件
    os.rename('service/read_word.py.tmp', 'service/read_word.py')
    os.rename('service/base64_to_file.py.tmp', 'service/base64_to_file.py')