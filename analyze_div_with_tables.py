#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_div_with_tables():
    """分析包含表格的div结构"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取body元素
    body = html_tree.find('.//body')
    
    # 查找所有包含表格的div
    divs_with_tables = body.xpath('.//div[.//table]')
    
    print(f"找到 {len(divs_with_tables)} 个包含表格的div")
    
    for i, div in enumerate(divs_with_tables):
        print(f"\n=== 包含表格的div {i+1} ===")
        
        # 获取div的直接子元素
        children = list(div)
        print(f"直接子元素数量: {len(children)}")
        
        # 分析每个子元素
        for j, child in enumerate(children):
            print(f"  子元素 {j+1}: {child.tag}")
            
            if child.tag == 'table':
                print(f"    这是一个表格")
                # 检查表格中是否有绕组
                winding_text = child.xpath('.//td[contains(.//text(), "绕组")]')
                print(f"    表格中包含绕组的单元格数量: {len(winding_text)}")
            else:
                # 检查这个子元素是否包含绕组文本
                if child.text_content().strip():
                    text_content = child.text_content().strip()
                    if '绕组' in text_content:
                        print(f"    包含绕组文本: {text_content[:50]}...")
                    else:
                        print(f"    文本内容: {text_content[:50]}...")
        
        # 获取div的完整文本内容
        full_text = div.text_content().strip()
        print(f"  div完整文本: {full_text[:100]}...")

if __name__ == "__main__":
    analyze_div_with_tables()