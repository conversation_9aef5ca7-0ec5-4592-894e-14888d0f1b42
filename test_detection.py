#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def test_detection_requirements():
    """测试检测要求和测量结果的重复问题"""
    
    # 检查是否存在转换后的JSON文件
    json_file = 'service/json/vform_from_html.json'
    if not os.path.exists(json_file):
        print("JSON文件不存在，需要先生成")
        return
    
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        vform_data = json.load(f)
    
    # 分析widgetList中的内容
    widgets = vform_data.get('widgetList', [])
    
    detection_texts = []
    measurement_texts = []
    
    def analyze_widget(widget, path=""):
        """分析组件"""
        if isinstance(widget, dict):
            widget_type = widget.get('type', '')
            
            if widget_type == 'static-text':
                text_content = widget.get('options', {}).get('textContent', '')
                if '检测要求' in text_content:
                    detection_texts.append({
                        'path': path,
                        'text': text_content[:100] + '...' if len(text_content) > 100 else text_content
                    })
                if '测量或观察结果' in text_content:
                    measurement_texts.append({
                        'path': path,
                        'text': text_content[:100] + '...' if len(text_content) > 100 else text_content
                    })
            
            # 递归分析子组件
            for key, value in widget.items():
                if key not in ['options', 'rows', 'cols']:
                    analyze_widget(value, f"{path}.{key}" if path else key)
        
        elif isinstance(widget, list):
            for i, item in enumerate(widget):
                analyze_widget(item, f"{path}[{i}]")
    
    # 分析所有组件
    for i, widget in enumerate(widgets):
        analyze_widget(widget, f"widgetList[{i}]")
    
    # 输出结果
    print("=== 检测要求和测量结果重复分析 ===")
    print(f"总组件数量: {len(widgets)}")
    print(f"包含'检测要求'的文本数量: {len(detection_texts)}")
    print(f"包含'测量或观察结果'的文本数量: {len(measurement_texts)}")
    
    print(f"\n=== 检测要求文本 ===")
    for i, text_info in enumerate(detection_texts):
        print(f"文本 {i+1}:")
        print(f"  路径: {text_info['path']}")
        print(f"  内容: {text_info['text']}")
    
    print(f"\n=== 测量或观察结果文本 ===")
    for i, text_info in enumerate(measurement_texts):
        print(f"文本 {i+1}:")
        print(f"  路径: {text_info['path']}")
        print(f"  内容: {text_info['text']}")
    
    # 检查是否有重复
    if len(detection_texts) > 1:
        print(f"\n⚠️  警告：发现多个包含'检测要求'的文本，存在重复问题")
    elif len(detection_texts) == 1:
        print(f"\n✅ 成功：只发现一个包含'检测要求'的文本")
    else:
        print(f"\n❌ 错误：没有找到包含'检测要求'的文本")
    
    if len(measurement_texts) > 1:
        print(f"⚠️  警告：发现多个包含'测量或观察结果'的文本，存在重复问题")
    elif len(measurement_texts) == 1:
        print(f"✅ 成功：只发现一个包含'测量或观察结果'的文本")
    else:
        print(f"❌ 错误：没有找到包含'测量或观察结果'的文本")

if __name__ == "__main__":
    test_detection_requirements()