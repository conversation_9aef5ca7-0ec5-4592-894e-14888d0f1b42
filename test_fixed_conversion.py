#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring
import json
import re

class SimpleTableParser:
    """简化的表格解析器，用于测试修复效果"""
    
    def __init__(self):
        self.counter = 1
    
    def generate_id(self):
        """生成唯一ID"""
        self.counter += 1
        return self.counter
    
    def _get_plain_text(self, element):
        """获取纯文本内容"""
        text = element.text_content() or ""
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def _create_static_text(self, text, align='center'):
        """创建静态文本组件"""
        text_id = self.generate_id()
        return {
            "key": text_id,
            "type": "static-text",
            "icon": "static-text",
            "formItemFlag": False,
            "options": {
                "name": f"statictext_{text_id}",
                "columnWidth": "200px",
                "hidden": False,
                "textContent": text,
                "textAlign": align,
                "fontSize": "13px",
                "preWrap": True,
                "customClass": [],
                "onCreated": "",
                "onMounted": "",
                "label": "static-text"
            },
            "id": f"statictext_{text_id}"
        }
    
    def _parse_cell_content(self, cell):
        """解析单元格内容（修复版）"""
        widgets = []
        
        # 获取单元格中的样式信息
        style = cell.get('style', '')
        text_align = 'center'
        
        # 解析对齐方式
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        # 检查单元格是否包含嵌套表格
        has_nested_table = cell.xpath('.//table')
        
        # 按HTML结构顺序处理单元格的所有子元素
        children = list(cell)
        
        if children:
            # 有子元素，按DOM顺序处理
            for child in children:
                # 如果单元格包含嵌套表格，跳过可能包含重复内容的div元素
                if has_nested_table and child.tag == 'div':
                    div_text = child.text_content().strip()
                    # 检查div内容是否包含嵌套表格的关键词
                    if any(keyword in div_text for keyword in ['检测要求', '测量或观察结果', '绕组']):
                        # 这个div可能包含嵌套表格的纯文本版本，跳过处理
                        continue
                
                # 处理段落
                if child.tag == 'p':
                    p_text = self._get_plain_text(child)
                    if p_text.strip():
                        widgets.append(self._create_static_text(p_text, text_align))
                
                # 处理嵌套表格
                elif child.tag == 'table':
                    nested_table = self.parse_table(child)
                    if nested_table:
                        widgets.append(nested_table)
        
        return widgets
    
    def parse_table(self, table):
        """解析表格"""
        # 获取所有行
        rows = table.xpath('.//tr')
        if not rows:
            return None
        
        table_id = self.generate_id()
        table_node = {
            "key": table_id,
            "type": "table",
            "category": "container",
            "icon": "table",
            "rows": [],
            "options": {
                "name": f"table-{table_id}",
                "hidden": False,
                "tableWidth": "100%",
                "customClass": []
            },
            "id": f"table-{table_id}"
        }
        
        # 处理每一行
        for row in rows:
            cells = row.xpath('./td|./th')
            if not cells:
                continue
            
            row_node = {
                "type": "table-row",
                "category": "container",
                "icon": "table-row",
                "internal": True,
                "merged": False,
                "cols": []
            }
            
            # 处理每个单元格
            for cell in cells:
                cell_widgets = self._parse_cell_content(cell)
                
                cell_node = {
                    "type": "table-cell",
                    "category": "container",
                    "icon": "table-cell",
                    "internal": True,
                    "widgetList": cell_widgets,
                    "merged": False,
                    "options": {
                        "name": f"table-cell-{self.generate_id()}",
                        "cellWidth": "auto",
                        "cellHeight": "",
                        "colspan": 1,
                        "rowspan": 1,
                        "customClass": []
                    }
                }
                
                row_node["cols"].append(cell_node)
            
            table_node["rows"].append(row_node)
        
        return table_node

def test_fixed_conversion():
    """测试修复后的转换效果"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取主表格
    main_table = html_tree.xpath('//table')[0]
    
    # 创建解析器
    parser = SimpleTableParser()
    
    # 解析主表格
    result = parser.parse_table(main_table)
    
    if result:
        # 保存结果
        with open('service/json/vform_from_html.json', 'w', encoding='utf-8') as f:
            json.dump({"widgetList": [result]}, f, ensure_ascii=False, indent=2)
        
        print("✅ 转换完成，结果已保存到 service/json/vform_from_html.json")
        
        # 分析结果
        def analyze_duplicates(obj, path="", results=None):
            if results is None:
                results = {'检测要求': [], '测量或观察结果': []}
            
            if isinstance(obj, dict):
                if obj.get('type') == 'static-text':
                    text_content = obj.get('options', {}).get('textContent', '')
                    if '检测要求' in text_content:
                        results['检测要求'].append(path)
                    if '测量或观察结果' in text_content:
                        results['测量或观察结果'].append(path)
                
                for key, value in obj.items():
                    new_path = f"{path}.{key}" if path else key
                    analyze_duplicates(value, new_path, results)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    new_path = f"{path}[{i}]" if path else f"[{i}]"
                    analyze_duplicates(item, new_path, results)
            
            return results
        
        duplicates = analyze_duplicates(result)
        
        print(f"\n=== 重复检查结果 ===")
        print(f"检测要求出现次数: {len(duplicates['检测要求'])}")
        print(f"测量或观察结果出现次数: {len(duplicates['测量或观察结果'])}")
        
        if len(duplicates['检测要求']) <= 1 and len(duplicates['测量或观察结果']) <= 1:
            print("✅ 修复成功：没有重复内容")
        else:
            print("❌ 修复失败：仍然存在重复内容")
            print(f"检测要求位置: {duplicates['检测要求']}")
            print(f"测量或观察结果位置: {duplicates['测量或观察结果']}")
    else:
        print("❌ 转换失败")

if __name__ == "__main__":
    test_fixed_conversion()