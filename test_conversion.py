#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.html_to_vform import HtmlToVFormConverter

def test_conversion():
    """测试转换功能"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 创建转换器
    converter = HtmlToVFormConverter()
    
    # 执行转换
    result = converter.convert(html_content)
    
    # 保存结果
    with open('service/json/vform_from_html.json', 'w', encoding='utf-8') as f:
        import json
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print("转换完成，结果已保存到 service/json/vform_from_html.json")

if __name__ == "__main__":
    test_conversion()