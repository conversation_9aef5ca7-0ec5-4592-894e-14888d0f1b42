#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml.html import fromstring
from service.enhanced_table_parser import Enhanced<PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_table_structure():
    """分析HTML中的表格结构"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 查找所有表格
    tables = html_tree.xpath('//table')
    
    print(f"HTML中找到 {len(tables)} 个表格")
    
    # 分析每个表格的结构
    for i, table in enumerate(tables):
        print(f"\n=== 表格 {i+1} ===")
        
        # 获取表格的行数
        rows = table.xpath('.//tr')
        print(f"行数: {len(rows)}")
        
        # 查找表格中的嵌套表格
        nested_tables = table.xpath('.//table')
        if nested_tables:
            print(f"包含 {len(nested_tables)} 个嵌套表格")
        
        # 查找包含"绕组"文本的单元格
        winding_cells = table.xpath('.//td[contains(text(), "绕组")]')
        if winding_cells:
            print(f"找到 {len(winding_cells)} 个包含'绕组'的单元格")
            for cell in winding_cells:
                print(f"  - 单元格内容: {cell.text_content().strip()}")
    
    # 测试表格解析器
    print(f"\n=== 测试表格解析器 ===")
    
    # 创建表格解析器
    parser = EnhancedTableParser()
    
    # 解析每个表格
    for i, table in enumerate(tables):
        print(f"\n解析表格 {i+1}:")
        try:
            result = parser.parse_table(table)
            print(f"  解析成功，表格key: {result['key']}")
            
            # 统计表格中的行数
            rows_count = len(result.get('rows', []))
            print(f"  生成行数: {rows_count}")
            
            # 查找包含"绕组"的静态文本
            def find_winding_text(obj, path=""):
                if isinstance(obj, dict):
                    if obj.get('type') == 'static-text' and '绕组' in obj.get('options', {}).get('textContent', ''):
                        print(f"  找到'绕组'文本: {obj['options']['textContent'][:50]}...")
                        print(f"    路径: {path}")
                    for k, v in obj.items():
                        find_winding_text(v, f"{path}.{k}" if path else k)
                elif isinstance(obj, list):
                    for i, v in enumerate(obj):
                        find_winding_text(v, f"{path}[{i}]")
            
            find_winding_text(result)
            
        except Exception as e:
            print(f"  解析失败: {e}")

if __name__ == "__main__":
    analyze_table_structure()