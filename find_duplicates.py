#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def find_duplicate_content():
    """查找重复内容的具体位置"""
    
    # 读取JSON文件
    json_file = 'service/json/vform_from_html.json'
    with open(json_file, 'r', encoding='utf-8') as f:
        vform_data = json.load(f)
    
    # 递归查找所有包含特定文本的路径
    def find_text_paths(obj, target_text, path="", results=None):
        if results is None:
            results = []
        
        if isinstance(obj, dict):
            # 检查当前对象是否包含目标文本
            if obj.get('type') == 'static-text':
                text_content = obj.get('options', {}).get('textContent', '')
                if target_text in text_content:
                    results.append({
                        'path': path,
                        'full_text': text_content,
                        'context': f"在 {path} 位置的静态文本"
                    })
            
            # 递归检查所有值
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                find_text_paths(value, target_text, new_path, results)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_path = f"{path}[{i}]" if path else f"[{i}]"
                find_text_paths(item, target_text, new_path, results)
        
        return results
    
    # 查找重复的文本
    detection_results = find_text_paths(vform_data, '检测要求')
    measurement_results = find_text_paths(vform_data, '测量或观察结果')
    
    print("=== 重复内容详细分析 ===")
    
    print(f"\n1. 检测要求 (找到 {len(detection_results)} 处):")
    for i, result in enumerate(detection_results):
        print(f"   位置 {i+1}: {result['path']}")
        print(f"   完整文本: {result['full_text']}")
        print(f"   上下文: {result['context']}")
        print()
    
    print(f"\n2. 测量或观察结果 (找到 {len(measurement_results)} 处):")
    for i, result in enumerate(measurement_results):
        print(f"   位置 {i+1}: {result['path']}")
        print(f"   完整文本: {result['full_text']}")
        print(f"   上下文: {result['context']}")
        print()
    
    # 分析重复的原因
    if len(detection_results) > 1:
        print("⚠️  检测要求重复分析:")
        print("   可能原因:")
        print("   - 嵌套表格被重复处理")
        print("   - 表格解析器在处理嵌套表格时没有正确跳过")
        print("   - 主转换器仍然在处理嵌套表格的内容")
    
    if len(measurement_results) > 1:
        print("⚠️  测量或观察结果重复分析:")
        print("   可能原因:")
        print("   - 同一个表格内容被多次解析")
        print("   - 嵌套表格和父表格的内容都被独立处理")
    
    # 检查这些重复内容是否在嵌套表格中
    print(f"\n=== 嵌套表格分析 ===")
    
    def find_nested_tables(obj, path="", results=None):
        if results is None:
            results = []
        
        if isinstance(obj, dict):
            if obj.get('type') == 'table':
                # 检查这个表格是否在另一个表格中
                if 'rows' in path and 'cols' in path:
                    results.append({
                        'path': path,
                        'context': '嵌套表格'
                    })
                else:
                    results.append({
                        'path': path,
                        'context': '顶层表格'
                    })
            
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                find_nested_tables(value, new_path, results)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_path = f"{path}[{i}]" if path else f"[{i}]"
                find_nested_tables(item, new_path, results)
        
        return results
    
    tables = find_nested_tables(vform_data)
    print(f"找到 {len(tables)} 个表格:")
    for table in tables:
        print(f"   {table['path']} ({table['context']})")

if __name__ == "__main__":
    find_duplicate_content()