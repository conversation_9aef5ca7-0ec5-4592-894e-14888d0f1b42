#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def count_tables_in_vform(vform_file):
    """统计Vform文件中的表格数量"""
    with open(vform_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计表格数量
    table_matches = re.findall(r'"type":\s*"table"', content)
    return len(table_matches)

def count_tables_in_html(html_file):
    """统计HTML文件中的表格数量"""
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计表格标签数量
    table_tags = re.findall(r'<table', content)
    return len(table_tags)

def main():
    print("=== 表格重复问题验证 ===")
    
    # 统计HTML中的表格数量
    html_tables = count_tables_in_html('service/json/converted_to_html.html')
    print(f"HTML中的表格数量: {html_tables}")
    
    # 统计Vform中的表格数量
    vform_tables = count_tables_in_vform('service/json/vform_from_html.json')
    print(f"Vform中的表格数量: {vform_tables}")
    
    if vform_tables > html_tables:
        print(f"❌ 发现表格重复问题！多生成了 {vform_tables - html_tables} 个表格")
        return False
    elif vform_tables == html_tables:
        print("✅ 表格数量正确，没有重复问题")
        return True
    else:
        print(f"⚠️  表格数量不匹配，可能存在处理问题")
        return False

if __name__ == "__main__":
    main()