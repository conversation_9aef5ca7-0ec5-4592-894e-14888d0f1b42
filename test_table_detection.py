#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def test_table_detection():
    """测试表格检测"""
    print("=== 测试表格检测 ===")
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    html_tree = fromstring(html_content)
    
    # 查找所有表格
    all_tables = html_tree.xpath('//table')
    print(f"找到 {len(all_tables)} 个表格")
    
    # 创建一个测试处理器
    processed_tables = set()
    
    def process_element(element, depth=0):
        """处理元素"""
        indent = "  " * depth
        element_tag = element.tag if hasattr(element, 'tag') else 'unknown'
        
        # 处理当前元素中的表格
        direct_tables = element.xpath('./table')
        if direct_tables:
            print(f"{indent}{element_tag} 中找到 {len(direct_tables)} 个直接子表格:")
            for table in direct_tables:
                table_id = id(table)
                if table_id in processed_tables:
                    print(f"{indent}  表格 {table_id} 已处理过，跳过")
                else:
                    processed_tables.add(table_id)
                    print(f"{indent}  处理表格 {table_id}")
                    
                    # 检查表格中的嵌套表格
                    nested_tables = table.xpath('.//table')
                    if nested_tables:
                        print(f"{indent}    表格中有 {len(nested_tables)} 个嵌套表格")
        
        # 递归处理子元素
        child_nodes = element.xpath('node()')
        for node in child_nodes:
            if hasattr(node, 'tag'):
                process_element(node, depth + 1)
    
    # 从body开始处理
    body = html_tree.find('.//body')
    if body is not None:
        process_element(body)
    
    print(f"\n总共处理了 {len(processed_tables)} 个表格")
    return len(processed_tables)

if __name__ == "__main__":
    count = test_table_detection()
    if count == 3:
        print("✅ 成功找到所有3个表格！")
    else:
        print(f"❌ 只找到 {count} 个表格")