#!/usr/bin/env python3
"""
简单的表格重复处理测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml.html import fromstring

def test_table_detection():
    """测试表格检测逻辑"""
    
    # 创建包含嵌套表格的HTML
    html_content = """
    <html>
    <body>
        <table>
            <tr>
                <td>外层表格单元格1</td>
                <td>
                    <table>
                        <tr>
                            <td>嵌套表格单元格1</td>
                            <td>嵌套表格单元格2</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    
    print("=== 测试表格检测逻辑 ===")
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 统计HTML中的表格数量
    all_tables = html_tree.xpath('//table')
    top_level_tables = html_tree.xpath('//table[not(ancestor::table)]')
    nested_tables = html_tree.xpath('//table[ancestor::table]')
    
    print(f"HTML中总表格数量: {len(all_tables)}")
    print(f"顶层表格数量: {len(top_level_tables)}")
    print(f"嵌套表格数量: {len(nested_tables)}")
    
    # 模拟修复后的处理逻辑
    print(f"\n=== 模拟处理逻辑 ===")
    
    # 模拟 _process_body_content 的递归逻辑
    def simulate_process_body_content(element, depth=0):
        """模拟递归处理body内容"""
        processed = 0
        skipped = 0
        indent = "  " * depth

        child_nodes = element.xpath('node()')

        for node in child_nodes:
            if hasattr(node, 'tag'):
                if node.tag == 'table':
                    # 检查是否为嵌套表格
                    ancestor_tables = node.xpath('./ancestor::table')
                    if not ancestor_tables:
                        print(f"{indent}处理顶层表格")
                        processed += 1
                    else:
                        print(f"{indent}跳过嵌套表格")
                        skipped += 1
                elif node.tag in ['div', 'td', 'th']:
                    # 递归处理容器元素
                    sub_processed, sub_skipped = simulate_process_body_content(node, depth + 1)
                    processed += sub_processed
                    skipped += sub_skipped

        return processed, skipped

    body = html_tree.find('.//body')
    processed_tables, skipped_tables = simulate_process_body_content(body)
    
    print(f"处理的表格数量: {processed_tables}")
    print(f"跳过的表格数量: {skipped_tables}")
    
    # 验证结果
    expected_processed = len(top_level_tables)
    expected_skipped = len(nested_tables)
    
    print(f"\n=== 验证结果 ===")
    print(f"期望处理的表格数量: {expected_processed}")
    print(f"实际处理的表格数量: {processed_tables}")
    print(f"期望跳过的表格数量: {expected_skipped}")
    print(f"实际跳过的表格数量: {skipped_tables}")
    
    success = (processed_tables == expected_processed and 
               skipped_tables == expected_skipped)
    
    if success:
        print("✓ 表格处理逻辑正确")
    else:
        print("✗ 表格处理逻辑有问题")
    
    return success

if __name__ == "__main__":
    success = test_table_detection()
    if success:
        print("\n🎉 表格检测逻辑测试通过！")
    else:
        print("\n❌ 表格检测逻辑测试失败！")
