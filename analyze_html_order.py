#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_html_element_order():
    """分析HTML元素的原始顺序"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取body元素
    body = html_tree.find('.//body')
    
    print("=== HTML元素原始顺序分析 ===")
    
    def analyze_element(element, depth=0, path=""):
        """分析元素及其子元素的顺序"""
        indent = "  " * depth
        
        if hasattr(element, 'tag'):
            # 获取元素的路径
            current_path = f"{path}/{element.tag}" if path else element.tag
            
            # 获取元素的直接文本内容
            direct_text = element.text or ""
            
            # 获取元素的完整文本内容
            full_text = element.text_content().strip()
            
            print(f"{indent}元素: {element.tag}")
            print(f"{indent}  路径: {current_path}")
            
            if direct_text.strip():
                print(f"{indent}  直接文本: '{direct_text.strip()[:50]}...'")
            
            if full_text:
                print(f"{indent}  完整文本: '{full_text[:50]}...'")
            
            # 特殊处理表格
            if element.tag == 'table':
                # 检查是否为嵌套表格
                ancestor_tables = element.xpath('./ancestor::table')
                is_nested = len(ancestor_tables) > 0
                print(f"{indent}  表格类型: {'嵌套表格' if is_nested else '顶层表格'}")
                
                # 获取表格的行
                rows = element.xpath('.//tr')
                print(f"{indent}  行数: {len(rows)}")
            
            # 分析子元素
            children = list(element)
            if children:
                print(f"{indent}  子元素数量: {len(children)}")
                
                for i, child in enumerate(children):
                    print(f"{indent}  子元素 {i+1}: {child.tag}")
                    
                    # 如果是表格，特别标记
                    if child.tag == 'table':
                        ancestor_tables = child.xpath('./ancestor::table')
                        is_nested = len(ancestor_tables) > 0
                        print(f"{indent}    类型: {'嵌套表格' if is_nested else '顶层表格'}")
                    
                    # 递归分析子元素
                    analyze_element(child, depth + 2, f"{current_path}[{i}]")
            
            # 获取元素的尾部文本
            if element.tail and element.tail.strip():
                print(f"{indent}  尾部文本: '{element.tail.strip()[:50]}...'")
            
            print()
    
    # 分析body的所有直接子元素
    if body is not None:
        children = list(body)
        print(f"Body直接子元素数量: {len(children)}")
        
        for i, child in enumerate(children):
            print(f"\n=== Body子元素 {i+1} ===")
            analyze_element(child, 0, f"body[{i}]")

if __name__ == "__main__":
    analyze_html_element_order()