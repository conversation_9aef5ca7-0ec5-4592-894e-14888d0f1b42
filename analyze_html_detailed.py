#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring

def analyze_html_table_structure():
    """分析HTML中表格的具体结构"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取所有表格
    tables = html_tree.xpath('//table')
    
    print("=== HTML表格结构详细分析 ===")
    print(f"找到 {len(tables)} 个表格")
    
    for i, table in enumerate(tables):
        print(f"\n--- 表格 {i+1} ---")
        
        # 获取表格的路径
        ancestor_tables = table.xpath('./ancestor::table')
        depth = len(ancestor_tables)
        print(f"嵌套深度: {depth}")
        
        # 获取所有行
        rows = table.xpath('.//tr')
        print(f"行数: {len(rows)}")
        
        # 分析每一行
        for row_idx, row in enumerate(rows):
            cells = row.xpath('./td|./th')
            print(f"  行 {row_idx+1}: {len(cells)} 个单元格")
            
            for cell_idx, cell in enumerate(cells):
                cell_text = cell.text_content().strip()
                if cell_text:
                    print(f"    单元格 {cell_idx+1}: {cell_text[:50]}...")
                
                # 检查单元格中是否有嵌套表格
                nested_in_cell = cell.xpath('.//table')
                if nested_in_cell:
                    print(f"    单元格 {cell_idx+1} 包含 {len(nested_in_cell)} 个嵌套表格")
        
        # 查找包含特定文本的单元格
        detection_cells = table.xpath('.//td[contains(text(), "检测要求")]')
        measurement_cells = table.xpath('.//td[contains(text(), "测量或观察结果")]')
        
        if detection_cells:
            print(f"  包含'检测要求'的单元格: {len(detection_cells)} 个")
            for cell in detection_cells:
                print(f"    内容: {cell.text_content().strip()}")
        
        if measurement_cells:
            print(f"  包含'测量或观察结果'的单元格: {len(measurement_cells)} 个")
            for cell in measurement_cells:
                print(f"    内容: {cell.text_content().strip()}")
    
    # 分析主表格的第2行和第3行
    print(f"\n=== 主表格关键行分析 ===")
    main_table = tables[0]  # 假设第一个是主表格
    rows = main_table.xpath('.//tr')
    
    if len(rows) >= 3:
        # 分析第2行 (index 1)
        row_2 = rows[1]
        print(f"\n第2行分析:")
        cells_2 = row_2.xpath('./td|./th')
        for i, cell in enumerate(cells_2):
            cell_content = cell.text_content().strip()
            nested_tables = cell.xpath('.//table')
            print(f"  单元格 {i+1}: '{cell_content}'")
            if nested_tables:
                print(f"    包含 {len(nested_tables)} 个嵌套表格")
        
        # 分析第3行 (index 2)
        row_3 = rows[2]
        print(f"\n第3行分析:")
        cells_3 = row_3.xpath('./td|./th')
        for i, cell in enumerate(cells_3):
            cell_content = cell.text_content().strip()
            print(f"  单元格 {i+1}: '{cell_content}'")

if __name__ == "__main__":
    analyze_html_table_structure()