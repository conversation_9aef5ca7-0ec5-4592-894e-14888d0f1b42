#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml.html import fromstring
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 直接导入和测试表格解析器
def test_table_parser():
    """测试表格解析器的修复效果"""
    
    # 读取HTML文件
    with open('service/json/converted_to_html.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 解析HTML
    html_tree = fromstring(html_content)
    
    # 获取主表格
    main_table = html_tree.xpath('//table')[0]
    
    print("=== 测试表格解析器修复效果 ===")
    
    # 模拟修复后的表格解析逻辑
    def test_cell_processing(cell):
        """测试单元格处理逻辑"""
        print(f"\n处理单元格:")
        
        # 检查单元格是否包含嵌套表格
        has_nested_table = cell.xpath('.//table')
        print(f"  包含嵌套表格: {len(has_nested_table) > 0}")
        
        # 获取所有子元素
        children = list(cell)
        print(f"  子元素数量: {len(children)}")
        
        # 模拟修复后的处理逻辑
        processed_widgets = []
        skipped_divs = []
        
        for i, child in enumerate(children):
            if has_nested_table and child.tag == 'div':
                div_text = child.text_content().strip()
                if any(keyword in div_text for keyword in ['检测要求', '测量或观察结果', '绕组']):
                    skipped_divs.append({
                        'index': i,
                        'tag': child.tag,
                        'text_preview': div_text[:100] + '...' if len(div_text) > 100 else div_text
                    })
                    continue
            
            # 处理其他元素
            if child.tag == 'table':
                processed_widgets.append({
                    'type': 'nested_table',
                    'index': i
                })
            elif child.tag == 'p':
                processed_widgets.append({
                    'type': 'paragraph',
                    'index': i
                })
            else:
                processed_widgets.append({
                    'type': child.tag,
                    'index': i
                })
        
        print(f"  处理的组件: {len(processed_widgets)}")
        print(f"  跳过的div: {len(skipped_divs)}")
        
        if skipped_divs:
            print("  跳过的div内容:")
            for div in skipped_divs:
                print(f"    div[{div['index']}]: {div['text_preview']}")
        
        return processed_widgets, skipped_divs
    
    # 测试主表格的第2行第1个单元格（问题单元格）
    rows = main_table.xpath('.//tr')
    if len(rows) >= 2:
        problematic_cell = rows[1].xpath('./td')[0]
        processed_widgets, skipped_divs = test_cell_processing(problematic_cell)
        
        print(f"\n=== 修复效果分析 ===")
        print(f"修复前: 会处理所有子元素，包括包含重复内容的div")
        print(f"修复后: 跳过了 {len(skipped_divs)} 个包含重复内容的div")
        print(f"保留的组件: {len(processed_widgets)} 个（包括嵌套表格和其他有效内容）")
        
        if skipped_divs:
            print(f"\n✅ 修复成功: 跳过了包含重复内容的div，应该能解决重复问题")
        else:
            print(f"\n❌ 修复失败: 没有跳过任何div")

if __name__ == "__main__":
    test_table_parser()