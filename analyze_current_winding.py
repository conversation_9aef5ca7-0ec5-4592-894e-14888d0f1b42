#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def analyze_current_winding():
    """分析当前vForm中的绕组分布"""
    
    # 读取JSON文件
    with open('service/json/vform_from_html.json', 'r', encoding='utf-8') as f:
        vform_data = json.load(f)
    
    # 统计绕组出现情况
    winding_count = 0
    table_winding_count = {}
    
    # 遍历所有组件
    widget_list = vform_data.get('widgetList', [])
    
    for table_idx, table in enumerate(widget_list):
        if table.get('type') == 'table':
            print(f"\n=== 表格 {table_idx + 1} ===")
            table_windings = 0
            
            rows = table.get('rows', [])
            for row_idx, row in enumerate(rows):
                cols = row.get('cols', [])
                for col_idx, col in enumerate(cols):
                    widget_list_in_col = col.get('widgetList', [])
                    for widget_idx, widget in enumerate(widget_list_in_col):
                        if widget.get('type') == 'static-text':
                            text_content = widget.get('options', {}).get('textContent', '')
                            if '绕组' in text_content:
                                winding_count += 1
                                table_windings += 1
                                print(f"  绕组 {winding_count}: 行{row_idx}, 列{col_idx}")
                                print(f"    内容: {text_content[:80]}...")
            
            table_winding_count[table_idx] = table_windings
            print(f"  表格 {table_idx + 1} 绕组总数: {table_windings}")
    
    print(f"\n=== 总结 ===")
    print(f"总绕组出现次数: {winding_count}")
    print(f"表格数量: {len(table_winding_count)}")
    
    # 分析是否有重复
    for table_idx, count in table_winding_count.items():
        print(f"表格 {table_idx + 1}: {count} 个绕组")
    
    # 检查是否有异常多的绕组
    if winding_count > 10:
        print(f"⚠️ 绕组出现次数过多 ({winding_count})，可能存在重复")
    else:
        print(f"✅ 绕组出现次数正常 ({winding_count})")

if __name__ == "__main__":
    analyze_current_winding()